<?php

namespace MatGyver\Entity\Tarteaucitron;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatG<PERSON>ver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;

#[ORM\Table(name: 'mg_tarteaucitron_consents')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Tarteaucitron\TarteaucitronConsentRepository::class)]
class TarteaucitronConsent
{

    const RESPONSE_ALLOWED = 'allowed';
    const RESPONSE_DENIED = 'denied';

    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $service;

    #[ORM\Column(type: 'text')]
    private $url;

    #[ORM\Column(type: 'string', length: 50)]
    private $response;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    /**
     * @Gedmo\Timestampable(on="create")
     */
    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getService(): ?string
    {
        return $this->service;
    }

    public function setService(string $service): self
    {
        $this->service = $service;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(?string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getResponse(): ?string
    {
        return $this->response;
    }

    public function setResponse(string $response): self
    {
        $this->response = $response;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
