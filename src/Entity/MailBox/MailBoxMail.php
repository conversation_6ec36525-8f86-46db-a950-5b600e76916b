<?php

namespace MatGyver\Entity\MailBox;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\User\User;

#[ORM\Table(name: 'mailbox_mails')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\MailBox\MailBoxMailRepository::class)]
class MailBoxMail
{
    const FLAG_IMPORTANT = 'important';
    const FLAG_SENT = 'sent';

    const STATUS_CLOSE = 'close';
    const STATUS_OPEN = 'open';
    const STATUS_WAITING_TO_BE_PROCESSED = 'waiting_to_be_processed';
    const STATUS_WAITING_ANSWER = 'waiting_answer';
    const STATUS_ARCHIVED = 'archived';
    const STATUS_DELETED = 'deleted';
    const STATUS_DRAFT = 'draft';

    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';

    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Dossier::class)]
    private $dossier;

    #[ORM\JoinColumn(nullable: false, name: 'sender_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $sender;

    #[ORM\Column(type: 'string')]
    private $firstName = '';

    #[ORM\Column(type: 'string')]
    private $lastName = '';

    #[ORM\Column(type: 'string')]
    private $email = '';

    #[ORM\Column(type: 'string', length: 200, nullable: true)]
    private $subject;

    #[ORM\Column(type: 'text')]
    private $message;

    #[ORM\Column(type: 'string', length: 20)]
    private $priority = self::PRIORITY_LOW;

    #[ORM\Column(type: 'string', length: 50)]
    private $status;

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private $flag;

    /**
     * @Gedmo\Timestampable(on="create")
     */
    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $dateClosed;

    #[ORM\OneToMany(targetEntity: MailBoxReply::class, mappedBy: 'mail')]
     #[ORM\OrderBy(['date' => 'ASC'])]
     private $replies;

    #[ORM\OneToMany(targetEntity: MailBoxRecipient::class, mappedBy: 'mail')]
    private $recipients;

    #[ORM\JoinTable(name: 'mailbox_documents')]
    #[ORM\JoinColumn(name: 'mail_id', referencedColumnName: 'id')]
    #[ORM\InverseJoinColumn(name: 'document_id', referencedColumnName: 'id')]
    #[ORM\ManyToMany(targetEntity: \MatGyver\Entity\Dossier\DossierDocument::class)]
    private $documents;

    public function __construct()
    {
        $this->replies = new ArrayCollection();
        $this->recipients = new ArrayCollection();
        $this->documents = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getSender(): ?User
    {
        if ($this->sender) {
            try {
                $this->sender->getFirstName();
            } catch (\Exception $e) {
                $this->sender = null;
            }
        }

        return $this->sender;
    }

    public function setSender(?User $sender): MailBoxMail
    {
        $this->sender = $sender;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getPriority(): ?string
    {
        return $this->priority;
    }

    public function setPriority(string $priority): self
    {
        $this->priority = $priority;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getFlag(): ?string
    {
        return $this->flag;
    }

    public function setFlag(?string $flag): self
    {
        $this->flag = $flag;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateClosed(): ?\DateTimeInterface
    {
        return $this->dateClosed;
    }

    public function setDateClosed(\DateTimeInterface $dateClosed): self
    {
        $this->dateClosed = $dateClosed;

        return $this;
    }

    /**
     * @return Collection<int, MailBoxReply>
     */
    public function getReplies(): Collection
    {
        return $this->replies;
    }

    public function getLastReply(): ?MailBoxReply
    {
        return (count($this->replies) ? $this->replies->last() : null);
    }

    public function addReply(MailBoxReply $reply): self
    {
        if (!$this->replies->contains($reply)) {
            $this->replies[] = $reply;
            $reply->setMail($this);
        }

        return $this;
    }

    public function removeReply(MailBoxReply $reply): self
    {
        if ($this->replies->removeElement($reply)) {
            // set the owning side to null (unless already changed)
            if ($reply->getMail() === $this) {
                $reply->setMail(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, MailBoxRecipient>
     */
    public function getRecipients(): Collection
    {
        return $this->recipients;
    }

    public function addRecipient(MailBoxRecipient $recipient): self
    {
        if (!$this->recipients->contains($recipient)) {
            $this->recipients[] = $recipient;
            $recipient->setMail($this);
        }

        return $this;
    }

    public function removeRecipient(MailBoxRecipient $recipient): self
    {
        if ($this->recipients->removeElement($recipient)) {
            // set the owning side to null (unless already changed)
            if ($recipient->getMail() === $this) {
                $recipient->setMail(null);
            }
        }

        return $this;
    }

    public function renderRecipients(string $separator = ', '): string
    {
        $recipients = [];
        foreach ($this->getRecipients() as $recipient) {
            $user = $recipient->getUser();
            if ($user) {
                try {
                    $user->getFirstName();
                } catch (\Exception $e) {
                    $user = null;
                }
            }
            $recipients[] = ($user ? $user->getFirstName() . ' ' . $user->getLastName() : $recipient->getFirstName() . ' ' . $recipient->getLastName());
        }

        return implode($separator, $recipients);
    }

    /**
     * @return string
     */
    public function renderStatus(): string
    {
        return match ($this->getStatus()) {
            self::STATUS_OPEN => '<span class="label label-light-primary font-weight-bold label-inline mr-1">' . __('En cours') . '</span>',
            self::STATUS_CLOSE => '<span class="label label-light-success font-weight-bold label-inline mr-1">' . __('Clôturé') . '</span>',
            self::STATUS_ARCHIVED => '<span class="label label-light-warning font-weight-bold label-inline mr-1">' . __('Archivé') . '</span>',
            self::STATUS_DELETED => '<span class="label label-light-danger font-weight-bold label-inline mr-1">' . __('Supprimé') . '</span>',
            self::STATUS_WAITING_TO_BE_PROCESSED => '<span class="label label-light-info font-weight-bold label-inline mr-1">' . __('En attente de traitement') . '</span>',
            self::STATUS_WAITING_ANSWER => '<span class="label label-light-primary font-weight-bold label-inline mr-1">' . __('En attente d\'une réponse') . '</span>',
            self::STATUS_DRAFT => '<span class="label label-light-info font-weight-bold label-inline mr-1">' . __('Programmé') . '</span>',
            default => '<span class="label-light-warning font-weight-bold label-inline mr-1">' . $this->getStatus() . '</span>',
        };
    }

    /**
     * @return string
     */
    public function renderPriorityLabel(): string
    {
        return match ($this->getPriority()) {
            self::PRIORITY_LOW => '<span class="label label-default label-inline mr-1">' . __('Basse') . '</span>',
            self::PRIORITY_MEDIUM => '<span class="label label-success label-inline mr-1">' . __('Moyenne') . '</span>',
            self::PRIORITY_HIGH => '<span class="label label-danger label-inline mr-1">' . __('Haute') . '</span>',
            default => '<span class="label label-primary label-inline mr-1">' . $this->getPriority() . '</span>',
        };
    }

    public function hasAccess(): bool
    {
        return true;

        if ($this->getSender() and $this->getSender()->getId() == $_SESSION['user']['id']) {
            return true;
        }
        if ($this->getEmail() and $this->getSender()->getEmail() == $_SESSION['user']['email']) {
            return true;
        }

        $recipients = $this->getRecipients();
        foreach ($recipients as $recipient) {
            if ($recipient->getUser() and $recipient->getUser()->getId() == $_SESSION['user']['id']) {
                if (!$recipient->getOpened()) {
                    $recipient->setMailOpened();
                }
                return true;
            }
            if ($recipient->getEmail() and $recipient->getEmail() == $_SESSION['user']['email']) {
                if (!$recipient->getOpened()) {
                    $recipient->setMailOpened();
                }
                return true;
            }
        }

        return false;
    }

    public function isClosed(): bool
    {
        return $this->getStatus() === self::STATUS_CLOSE;
    }

    /**
     * @return Collection|DossierDocument[]
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(DossierDocument $document): self
    {
        if (!$this->documents->contains($document)) {
            $this->documents[] = $document;
        }

        return $this;
    }

    public function removeDocument(DossierDocument $document): self
    {
        if ($this->documents->contains($document)) {
            $this->documents->removeElement($document);
        }

        return $this;
    }
}
