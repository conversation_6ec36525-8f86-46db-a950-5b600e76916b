<?php

namespace MatGyver\Entity\PaymentMethod;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\MappedSuperclass;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use MatGyver\Entity\Traits\ClientEntity;

#[MappedSuperclass]
abstract class PaymentMethodCharge
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'integer')]
    private $cartId;

    #[ORM\Column(type: 'string', length: 100)]
    private $transactionReference;

    #[ORM\Column(type: 'integer')]
    private $subscriptionId;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    #[ORM\Column(type: 'string', length: 200)]
    private $product;

    #[ORM\Column(type: 'string', length: 10)]
    private $currency;

    #[ORM\Column(type: 'string', length: 200)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 200)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 200)]
    private $email;

    #[ORM\Column(type: 'text')]
    private $custom;

    #[ORM\Column(type: 'text')]
    private $checkout;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'text')]
    private $result;

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private $last4;

    #[ORM\Column(type: 'text')]
    private $error;

    /**
     * @Gedmo\Timestampable(on="create")
     */
    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCartId(): ?int
    {
        return $this->cartId;
    }

    public function setCartId(int $cartId): self
    {
        $this->cartId = $cartId;

        return $this;
    }

    public function getTransactionReference(): ?string
    {
        return $this->transactionReference;
    }

    public function setTransactionReference(string $transactionReference): self
    {
        $this->transactionReference = $transactionReference;

        return $this;
    }

    public function getSubscriptionId(): ?int
    {
        return $this->subscriptionId;
    }

    public function setSubscriptionId(int $subscriptionId): self
    {
        $this->subscriptionId = $subscriptionId;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getProduct(): ?string
    {
        return $this->product;
    }

    public function setProduct(string $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getCustom(): ?string
    {
        return $this->custom;
    }

    public function setCustom(string $custom): self
    {
        $this->custom = $custom;

        return $this;
    }

    public function getCheckout(): ?string
    {
        return $this->checkout;
    }

    public function setCheckout(string $checkout): self
    {
        $this->checkout = $checkout;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getResult(): ?string
    {
        return $this->result;
    }

    public function setResult(string $result): self
    {
        $this->result = $result;

        return $this;
    }

    public function getLast4(): ?string
    {
        return $this->last4;
    }

    public function setLast4(?string $last4): self
    {
        $this->last4 = $last4;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(string $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
