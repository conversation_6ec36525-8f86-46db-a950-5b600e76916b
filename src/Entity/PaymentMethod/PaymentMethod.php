<?php

namespace MatGyver\Entity\PaymentMethod;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Traits\ClientEntity;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'mg_payments_methods')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\PaymentMethod\PaymentMethodRepository::class)]
class PaymentMethod
{
    use ClientEntity;

    const TYPE_STRIPE = 'stripe';
    const TYPE_MOLLIE = 'mollie';
    const TYPE_BRAINTREE = 'braintree';
    const STATUS_ACTIVE = 'active';
    const STATUS_EXPIRED = 'expired';
    const STATUS_ERROR = 'error';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $type;

    #[ORM\Column(type: 'string', length: 50)]
    private $customerId;

    #[ORM\Column(type: 'string', length: 50)]
    private $paymentMethodId;

    #[ORM\Column(type: 'string', length: 20)]
    private $last4;

    #[ORM\Column(type: 'boolean')]
    private $byDefault = false;

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    /**
     * @Gedmo\Timestampable(on="create")
     */
    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }
    
    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }
    
    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getPaymentMethodId(): ?string
    {
        return $this->paymentMethodId;
    }

    public function setPaymentMethodId(string $paymentMethodId): self
    {
        $this->paymentMethodId = $paymentMethodId;

        return $this;
    }

    public function getLast4(): ?string
    {
        return $this->last4;
    }

    public function setLast4(string $last4): self
    {
        $this->last4 = $last4;

        return $this;
    }

    public function getByDefault(): bool
    {
        return $this->byDefault;
    }

    public function setByDefault(bool $byDefault): self
    {
        $this->byDefault = $byDefault;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }
}
