<?php

namespace MatGyver\Entity\PaymentMethod;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\MappedSuperclass;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use MatG<PERSON>ver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\SoftDeleteableEntity;

/**
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false)
 */
#[MappedSuperclass]
abstract class PaymentMethodSubscription
{
    use ClientEntity;
    use SoftDeleteableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 100)]
    private $transactionReference;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account;

    #[ORM\Column(type: 'string', length: 200)]
    private $product;

    #[ORM\Column(type: 'string', length: 10)]
    private $currency;

    #[ORM\Column(type: 'string', length: 200)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 200)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 200)]
    private $email;

    #[ORM\Column(type: 'text')]
    private $custom;

    #[ORM\Column(type: 'text')]
    private $checkout;

    #[ORM\Column(type: 'string', length: 50)]
    private $ip;

    #[ORM\Column(type: 'string', length: 3)]
    private $nbPayments;

    #[ORM\Column(type: 'string', length: 3)]
    private $nbPaymentsLeft;

    #[ORM\Column(type: 'string', length: 10)]
    private $decalage;

    #[ORM\Column(type: 'boolean')]
    private $valid;

    #[ORM\Column(type: 'integer')]
    private $reattempt;

    #[ORM\Column(type: 'text')]
    private $result;

    #[ORM\Column(type: 'text')]
    private $error;

    #[ORM\Column(type: 'date')]
    private $date;

    #[ORM\Column(type: 'string', length: 2)]
    private $hour;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTransactionReference(): ?string
    {
        return $this->transactionReference;
    }

    public function setTransactionReference(string $transactionReference): self
    {
        $this->transactionReference = $transactionReference;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getProduct(): ?string
    {
        return $this->product;
    }

    public function setProduct(string $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getCustom(): ?string
    {
        return $this->custom;
    }

    public function setCustom(string $custom): self
    {
        $this->custom = $custom;

        return $this;
    }

    public function getCheckout(): ?string
    {
        return $this->checkout;
    }

    public function setCheckout(string $checkout): self
    {
        $this->checkout = $checkout;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getNbPayments(): ?string
    {
        return $this->nbPayments;
    }

    public function setNbPayments(string $nbPayments): self
    {
        $this->nbPayments = $nbPayments;

        return $this;
    }

    public function getNbPaymentsLeft(): ?string
    {
        return $this->nbPaymentsLeft;
    }

    public function setNbPaymentsLeft(string $nbPaymentsLeft): self
    {
        $this->nbPaymentsLeft = $nbPaymentsLeft;

        return $this;
    }

    public function getDecalage(): ?string
    {
        return $this->decalage;
    }

    public function setDecalage(string $decalage): self
    {
        $this->decalage = $decalage;

        return $this;
    }

    public function getValid(): ?bool
    {
        return $this->valid;
    }

    public function setValid(bool $valid): self
    {
        $this->valid = $valid;

        return $this;
    }

    public function getReattempt(): ?int
    {
        return $this->reattempt;
    }

    public function setReattempt(int $reattempt): self
    {
        $this->reattempt = $reattempt;

        return $this;
    }

    public function getResult(): ?string
    {
        return $this->result;
    }

    public function setResult(string $result): self
    {
        $this->result = $result;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(string $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getHour(): ?string
    {
        return $this->hour;
    }

    public function setHour(string $hour): self
    {
        $this->hour = $hour;

        return $this;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        $class = new \ReflectionClass($this);
        return str_replace([$class->getNamespaceName() . '\\', 'Subscription'], '', get_class($this));
    }

    abstract public function getAmount();
}
